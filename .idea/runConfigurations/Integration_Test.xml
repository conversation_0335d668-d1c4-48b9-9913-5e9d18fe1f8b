<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Integration Test" type="JUnit" factoryName="JUnit">
    <module name="nova-offer-state-writer.nova-offer-state-test.integration" />
    <useClassPathOnly />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" />
      </ENTRIES>
    </extension>
    <option name="PACKAGE_NAME" value="com.loyalty.nova.offer.state.writer.test" />
    <option name="MAIN_CLASS_NAME" value="com.loyalty.nova.offer.state.writer.test.OfferStateIT" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="tags" />
    <option name="VM_PARAMETERS" value="-ea -Dcucumber.options=&quot;--tags @local&quot; -Dspring.profiles.active=local --add-modules java.sql" />
    <option name="PARAMETERS" value="" />
    <tag value="it" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>