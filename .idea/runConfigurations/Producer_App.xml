<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Producer App" type="JetRunConfigurationType" factoryName="Kotlin">
    <module name="nova-offer-state-writer.nova-offer-state-writer-producer.main" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=local" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.loyalty.nova.offer.state.writer.ProducerAppKt" />
    <option name="WORKING_DIRECTORY" value="" />
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="mngr" />
      <env name="AWS_SECRET_ACCESS_KEY" value="mngr" />
      <env name="KINESIS_NAME" value="TEST_STREAM" />
    </envs>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>