{"source_info": {"data_source": "nova", "data_set": "state_changed", "data_type": "raw", "description": "State Change Events from State Service", "source_location": "State Service", "data_steward": {"name": "Team Things", "email": "<EMAIL>"}}, "governance": {"data_classification": "yellow"}, "data_profile": {"coverage_start": "2020-05-25", "coverage_end": "ongoing"}, "tags": {"search_words": ["collector", "state", "offer-state"]}, "schema": {"format": "json", "file_extension": "json", "character_encoding": "utf8", "fields": [{"name": "id", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "meta", "type": "object", "description": "N/A", "nullable": "false", "fields": [{"name": "version", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "correlationId", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "partition<PERSON>ey", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "originClient", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "timestamp", "type": "string", "description": "N/A", "nullable": "false", "date_format": "yyyy-MM-dd'T'hh:mm:ss'Z'"}]}, {"name": "data", "type": "object", "description": "N/A", "nullable": "false", "fields": [{"name": "eventType", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "collectorId", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "offerId", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "name", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "value", "type": "string", "description": "N/A", "nullable": "false"}, {"name": "properties", "type": "object", "description": "N/A", "nullable": "false", "fields": []}]}]}}