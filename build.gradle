plugins {
    id 'io.spring.dependency-management' apply false
    id 'org.jetbrains.kotlin.jvm' apply false
    id 'org.jetbrains.kotlin.plugin.spring' apply false
    id 'org.jetbrains.kotlin.plugin.jpa' apply false
    id 'org.springframework.boot' apply false
    id 'net.researchgate.release'
}

apply from: 'dependency.gradle'

if (!project.hasProperty("profiles")) {
    ext.profiles = "local"
}

buildScan {
    termsOfServiceUrl = 'https://gradle.com/terms-of-service'
    termsOfServiceAgree = 'yes'
}

configurations {
    detekt
}

dependencies {
    detekt "io.gitlab.arturbosch.detekt:detekt-cli"
}

task detekt(type: JavaExec) {
    main = "io.gitlab.arturbosch.detekt.cli.Main"
    classpath = configurations.detekt
    def input = "$rootDir"
    def config = "detekt.yml"
    def exclude = ".*/resources/.*,.*/build/.*"
    def report = "html:${project.buildDir}/reports/detekt.html"
    def params = ['-i', input, '-c', config, '-ex', exclude, '-r', report]
    args(params)
}

allprojects {
    group = 'com.loyalty.nova.offer.state.writer'
    version = "${version}"

    apply plugin: 'kotlin'
    apply plugin: 'io.spring.dependency-management'

    sourceCompatibility = 11

    targetCompatibility = 11

    compileKotlin {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = JavaVersion.VERSION_11.toString()
        }
    }

    compileTestKotlin {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = JavaVersion.VERSION_11.toString()
        }
    }

    repositories {
        repositories {
            mavenCentral()
            google()
            jcenter()
            maven { url 'https://dl.bintray.com/kotlin/kotlinx.html/' }
            maven { url "https://repo.spring.io/milestone" }
            maven { url 'https://packages.confluent.io/maven/' }
            maven { url "https://s3-us-west-2.amazonaws.com/dynamodb-local/release" }
            maven {
                url "https://loyalty.jfrog.io/loyalty/libs-release"
                credentials {
                    username = 'things'
                    password = 'AP2QQPyfCHZWKhshVbB2v4S79T1TNtoTPu3DW5ChcMCvjRVNX'
                }
            }
            maven {
                url "https://loyalty.jfrog.io/loyalty/libs-snapshot"
                credentials {
                    username = 'things'
                    password = 'AP2QQPyfCHZWKhshVbB2v4S79T1TNtoTPu3DW5ChcMCvjRVNX'
                }
            }
        }
    }

    dependencies {
        implementation(
                'com.amazonaws:amazon-kinesis-deaggregator',
                'com.amazonaws:aws-lambda-java-log4j2',
                'org.springframework.cloud:spring-cloud-function-adapter-aws',
                'org.springframework.cloud:spring-cloud-function-core',
                'org.springframework.cloud:spring-cloud-function-web',
                'com.amazonaws:aws-java-sdk-kinesis'
        )

        testImplementation(
                'org.junit.jupiter:junit-jupiter',
                'org.springframework.boot:spring-boot-starter-test',
                'org.mockito:mockito-core',
                'org.mockito:mockito-junit-jupiter',
                'org.hamcrest:java-hamcrest',
                'org.jetbrains.kotlin:kotlin-test'
        )

        testRuntimeOnly(
                'com.almworks.sqlite4java:sqlite4java'
        )
    }

    dependencyManagement {
        dependencies {
            libraries.each {
                library -> dependency library.value
            }
        }
        imports {
            mavenBom "io.projectreactor:reactor-bom:${versions.reactorBomVersion}"
            mavenBom "org.springframework.boot:spring-boot-starter-parent:${versions.springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${versions.springCloudVersion}"
        }
    }

    task allDependencies(type: DependencyReportTask) {}

    test {
        useJUnitPlatform {
            includeEngines 'junit-jupiter'
            excludeEngines 'junit-vintage'
        }
        // Enable to debug unit tests
        testLogging.showStandardStreams = false
        jvmArgs '-Dspring.profiles.active=test'
    }

    release {
        failOnSnapshotDependencies = true
        revertOnFail = true
        git {
            requireBranch = 'master'
        }
    }

    check.dependsOn detekt
}

