[{"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-prod-application"}, {"ParameterKey": "Environment", "ParameterValue": "prod"}, {"ParameterKey": "EventBusStackPrefix", "ParameterValue": "nova-common-events-state-changed-eventbus"}, {"ParameterKey": "KinesisForSplunkStackName", "ParameterValue": "prod-kinesissplunk"}, {"ParameterKey": "PagerDutyURLOfferStateProducer", "ParameterValue": "https://events.pagerduty.com/integration/ff66586799314af1a9696b4b41d41923/enqueue"}, {"ParameterKey": "ErroralarmThresholdProducer", "ParameterValue": 1}, {"ParameterKey": "DurationalarmThresholdProducer", "ParameterValue": 210000}, {"ParameterKey": "ThrottlealarmThresholdProducer", "ParameterValue": 1}]