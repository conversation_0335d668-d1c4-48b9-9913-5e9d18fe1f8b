[{"ParameterKey": "KMSKeyName", "ParameterValue": "amrpwl-nonprod-application"}, {"ParameterKey": "Environment", "ParameterValue": "sole"}, {"ParameterKey": "EventBusStackPrefix", "ParameterValue": "nova-offer-state-writer-eventbus"}, {"ParameterKey": "KinesisForSplunkStackName", "ParameterValue": "nonprod-kinesissplunk"}, {"ParameterKey": "PagerDutyURLOfferStateProducer", "ParameterValue": "https://events.pagerduty.com/integration/d132264d93f747baa79ead1a770f1935/enqueue"}, {"ParameterKey": "ErroralarmThresholdProducer", "ParameterValue": 1}, {"ParameterKey": "DurationalarmThresholdProducer", "ParameterValue": 210000}, {"ParameterKey": "ThrottlealarmThresholdProducer", "ParameterValue": 1}]