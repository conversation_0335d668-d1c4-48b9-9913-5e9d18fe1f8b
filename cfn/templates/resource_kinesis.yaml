AWSTemplateFormatVersion: 2010-09-09
Description: Template to create Kinesis Stream
Parameters:
  Environment:
    Type: String
    Description: Environment Name
    Default: dev
  StackPrefix:
    Type: String
    Default: nova-offer-state-writer
    Description: Name of CloudFormation stack prefix
  KMSKeyName:
    Description: The name of the KMS Key
    Type: String
    Default: amrpwl-nonprod-database
  RetentionPeriodHours:
    Type: Number
    Default: 24
  PagerDutyURLOfferStateProducer:
    Type: String
    Description: Pager Duty endpoint

Resources:
  Kinesis:
    Type: AWS::Kinesis::Stream
    Properties:
      Name: !Sub '${Environment}-${StackPrefix}-event-stream'
      StreamModeDetails:
          StreamMode: ON_DEMAND
      StreamEncryption:
        EncryptionType: KMS
        KeyId:
          Fn::ImportValue: !Ref KMSKeyName
      RetentionPeriodHours: !Ref RetentionPeriodHours

  AlarmSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      Subscription:
        - Endpoint:
            Fn::Sub: '${PagerDutyURLOfferStateProducer}'
          Protocol: https
      TopicName:
        Fn::Sub: '${AWS::StackName}-SNS-Alarm'

  RecordsRetentionAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: Checks if the records in stream are past certain age
      AlarmActions:
        - Ref: 'AlarmSNSTopic'
      OKActions:
        - Ref: 'AlarmSNSTopic'
      MetricName: GetRecords.IteratorAgeMilliseconds
      Namespace: AWS/Kinesis
      Statistic: Maximum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 60000
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: StreamName
          Value: !Ref Kinesis

Outputs:
  KinesisArn:
    Description: ARN for Kinesis EventBus to which consumers will listen
    Value: !GetAtt Kinesis.Arn
    Export:
      Name: !Sub '${AWS::StackName}-Kinesis-ARN'
  KinesisName:
    Description: Name for Kinesis EventBus to which consumers will listen
    Value: !Ref Kinesis
    Export:
      Name: !Sub '${AWS::StackName}-Kinesis-Name'
