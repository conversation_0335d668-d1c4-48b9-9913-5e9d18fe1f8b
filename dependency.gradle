ext.versions = [
        'apacheCommonsVersion'             : '1.9',
        'assertkVersion'                   : '0.22',
        'amazonKinesisClientVersion'       : '2.2.6',
        'awsLambdaJavaCoreVersion'         : '1.2.0',
        'awsLambdaJavaEventsVersion'       : '2.2.7',
        'awsJavaSdkVersion'                : '1.12.639',
        'awsServerlessJavaContainerVersion': '1.9.4',
        'amazonKinesisAggregatorVersion'   : '1.0.3',
        'amazonKinesisDeaggregatorVersion' : '1.0.3',
        'awsLambdaJavaLog4j2'              : '1.5.1',
        'cacheApiVersion'                  : '1.1.0',
        'cucumberVersion'                  : '4.2.0',
        'detektVersion'                    : '1.10.0',
        'dockerPluginVersion'              : '0.22.1',
        'DynamoDBLocalVersion'             : '1.11.119',
        'ehcacheVersion'                   : '3.6.2',
        'flywaydbVersion'                  : '5.2.4',
        'gradleEnterpriseVersion'          : '3.1',
        'gradleReleaseVersion'             : '2.6.0',
        'gradleTestLoggerPluginVersion'    : '1.5.0',
        'greenMailVersion'                 : '1.5.10',
        'guavaVersion'                     : '32.0.1-jre',
        'h2Version'                        : '1.4.199',
        'hamcrestVersion'                  : '2.0.0.0',
        'hibernateValidatorVersion'        : '6.0.10.Final',
        'httpClientVersion'                : '4.5.9',
        'jacksonDatabind'                  : '2.16.0',
        'jacksonVersion'                   : '2.16.0',
        'javafakerVersion'                 : '1.0.1',
        'jjwtVersion'                      : '0.10.7',
        'jmeterPluginsCasutgVersion'       : '2.6',
        'jmeterVersion'                    : '5.0',
        'jpamodelgenVersion'               : '5.4.10.Final',
        'junitJupiterVersion'              : '5.5.0',
        'kafkaAvroSerializerVersion'       : '5.3.0',
        'kotlintestVersion'                : '3.3.1',
        'kotlinVersion'                    : '1.6.10',
        'kotlinxJvmVersion'                : '0.7.3',
        'logbackClassicVersion'            : '1.2.13',
        'logbackContrib'                   : '0.1.5',
        'logbackExtensionsVersion'         : '0.1.5',
        'mockitoJunitJupiterVersion'       : '2.18.3',
        'mockitoVersion'                   : '2.23.0',
        'novaCommonEventsVersion'          : '0.0.157',
        'novaCommonTestVersion'            : '0.0.167',
        'novaCommonVersion'                : '0.0.57',
        'nettyVersion'                     : '4.1.100.Final',
        'nettyHttpVersion'                 : '4.1.109.Final',
        'postgresqlVersion'                : '42.2.9',
        'protobufJavaVersion'              : '3.21.12',
        'reactorBomVersion'                : 'Californium-RELEASE',
        'reactorKotlinExtensionsVersion'   : '1.0.2.RELEASE',
        'springBootVersion'                : '2.7.18',
        'springCloudVersion'               : 'Hoxton.SR5',
        'springDependencyManagementVersion': '1.0.9.RELEASE',
        'springMockVersion'                : '2.0.8',
        'sqlite4javaVersion'               : '1.0.392',
        'snakeYamlVersion'                 : '2.0',
        'swaggerVersion'                   : '3.0.0',
        'typesafeConfigVersion'            : '1.3.3',
        'wiremockVersion'                  : '2.21.0',
        'springContextVersion'             : '3.2.12',
        'apacheLog4j'                      : '2.23.1',
        'springCloudKafkaVersion'          : '3.2.10'
]

ext.libraries = [
        'apache-commons-text'                      : "org.apache.commons:commons-text:${versions.apacheCommonsVersion}",
        'amazon-kinesis-client'                    : "software.amazon.kinesis:amazon-kinesis-client:${versions.amazonKinesisClientVersion}",
        'amazon-kinesis-aggregator'                : "com.amazonaws:amazon-kinesis-aggregator:${versions.amazonKinesisAggregatorVersion}",
        'amazon-kinesis-deaggregator'              : "com.amazonaws:amazon-kinesis-deaggregator:${versions.amazonKinesisDeaggregatorVersion}",
        'aws-java-sdk-lambda'                      : "com.amazonaws:aws-java-sdk-lambda:${versions.awsJavaSdkVersion}",
        'aws-java-sdk-kinesis'                     : "com.amazonaws:aws-java-sdk-kinesis:${versions.awsJavaSdkVersion}",
        'aws-java-sdk-core'                        : "com.amazonaws:aws-java-sdk-core:${versions.awsJavaSdkVersion}",
        'aws-java-sdk-cloudwatch'                  : "com.amazonaws:aws-java-sdk-cloudwatch:${versions.awsJavaSdkVersion}",
        'aws-java-sdk-dynamodb'                    : "com.amazonaws:aws-java-sdk-dynamodb:${versions.awsJavaSdkVersion}",
        'aws-java-sdk-kms'                         : "com.amazonaws:aws-java-sdk-kms:${versions.awsJavaSdkVersion}",
        'aws-java-sdk-s3'                          : "com.amazonaws:aws-java-sdk-s3:${versions.awsJavaSdkVersion}",
        'ApacheJMeter_core'                        : "org.apache.jmeter:ApacheJMeter_core:${versions.jmeterVersion}",
        'ApacheJMeter_http'                        : "org.apache.jmeter:ApacheJMeter_http:${versions.jmeterVersion}",
        'assertk-jvm'                              : "com.willowtreeapps.assertk:assertk-jvm:${versions.assertkVersion}",
        'aws-lambda-java-core'                     : "com.amazonaws:aws-lambda-java-core:${versions.awsLambdaJavaCoreVersion}",
        'aws-lambda-java-events'                   : "com.amazonaws:aws-lambda-java-events:${versions.awsLambdaJavaEventsVersion}",
        'aws-serverless-java-container-springboot2': "com.amazonaws.serverless:aws-serverless-java-container-springboot2:${versions.awsServerlessJavaContainerVersion}",
        'aws-lambda-java-log4j2'                   : "com.amazonaws:aws-lambda-java-log4j2:${versions.awsLambdaJavaLog4j2}",
        'cache-api'                                : "javax.cache:cache-api:${versions.cacheApiVersion}",
        'cucumber-java8'                           : "io.cucumber:cucumber-java8:${versions.cucumberVersion}",
        'cucumber-junit'                           : "io.cucumber:cucumber-junit:${versions.cucumberVersion}",
        'cucumber-spring'                          : "io.cucumber:cucumber-spring:${versions.cucumberVersion}",
        'detekt-formatting'                        : "io.gitlab.arturbosch.detekt:detekt-formatting:${versions.detektVersion}",
        'detekt'                                   : "io.gitlab.arturbosch.detekt:detekt-cli:${versions.detektVersion}",
        'DynamoDBLocal'                            : "com.amazonaws:DynamoDBLocal:${versions.DynamoDBLocalVersion}",
        'ehcache'                                  : "org.ehcache:ehcache:${versions.ehcacheVersion}",
        'flywaydb'                                 : "org.flywaydb:flyway-core:${versions.flywaydbVersion}",
        'gradle-docker'                            : "gradle.plugin.com.palantir.gradle.docker:gradle-docker:${versions.dockerPluginVersion}",
        'gradle-release'                           : "net.researchgate:gradle-release:${versions.gradleReleaseVersion}",
        'gradle-test-logger-plugin'                : "com.adarshr:gradle-test-logger-plugin:${versions.gradleTestLoggerPluginVersion}",
        'greenmail'                                : "com.icegreen:greenmail:${versions.greenMailVersion}",
        'guava'                                    : "com.google.guava:guava:${versions.guavaVersion}",
        'h2'                                       : "com.h2database:h2:${versions.h2Version}",
        'hibernate-jpamodelgen'                    : "org.hibernate:hibernate-jpamodelgen:${versions.jpamodelgenVersion}",
        'hibernateValidator'                       : "org.hibernate.validator:hibernate-validator:${versions.hibernateValidatorVersion}",
        'httpclient'                               : "org.apache.httpcomponents:httpclient:${versions.httpClientVersion}",
        'jackson-annotations'                      : "com.fasterxml.jackson.core:jackson-annotations:${versions.jacksonVersion}",
        'jackson-core'                             : "com.fasterxml.jackson.core:jackson-core:${versions.jacksonVersion}",
        'jackson-databind'                         : "com.fasterxml.jackson.core:jackson-databind:${versions.jacksonDatabind}",
        'jackson-dataformat-cbor'                  : "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:${versions.jacksonVersion}",
        'jackson-module-kotlin'                    : "com.fasterxml.jackson.module:jackson-module-kotlin:${versions.jacksonVersion}",
        'java-hamcrest'                            : "org.hamcrest:java-hamcrest:${versions.hamcrestVersion}",
        'javafaker'                                : "com.github.javafaker:javafaker:${versions.javafakerVersion}",
        'jjwt-api'                                 : "io.jsonwebtoken:jjwt-api:${versions.jjwtVersion}",
        'jjwt-impl'                                : "io.jsonwebtoken:jjwt-impl:${versions.jjwtVersion}",
        'jjwt-jackson'                             : "io.jsonwebtoken:jjwt-jackson:${versions.jjwtVersion}",
        'jmeter-plugins-casutg'                    : "kg.apc:jmeter-plugins-casutg:${versions.jmeterPluginsCasutgVersion}",
        'junit-jupiter'                            : "org.junit.jupiter:junit-jupiter:${versions.junitJupiterVersion}",
        'kafka-avro-serializer'                    : "io.confluent:kafka-avro-serializer:${versions.kafkaAvroSerializerVersion}",
        'kotlin-allopen'                           : "org.jetbrains.kotlin:kotlin-allopen:${versions.kotlinVersion}",
        'kotlin-gradle-plugin'                     : "org.jetbrains.kotlin:kotlin-gradle-plugin:${versions.kotlinVersion}",
        'kotlin-noarg'                             : "org.jetbrains.kotlin:kotlin-noarg:${versions.kotlinVersion}",
        'kotlin-reflect'                           : "org.jetbrains.kotlin:kotlin-reflect:${versions.kotlinVersion}",
        'kotlin-scripting-jvm'                     : "org.jetbrains.kotlin:kotlin-scripting-jvm:${versions.kotlinVersion}",
        'kotlin-stdlib'                            : "org.jetbrains.kotlin:kotlin-stdlib:${versions.kotlinVersion}",
        'kotlin-stdlib-jdk7'                       : "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${versions.kotlinVersion}",
        'kotlin-stdlib-jdk8'                       : "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${versions.kotlinVersion}",
        'kotlin-test'                              : "org.jetbrains.kotlin:kotlin-test:${versions.kotlinVersion}",
        'kotlintest-extensions-spring'             : "io.kotlintest:kotlintest-extensions-spring:${versions.kotlintestVersion}",
        'kotlinx-html-jvm'                         : "org.jetbrains.kotlinx:kotlinx-html-jvm:${versions.kotlinxJvmVersion}",
        'libsqlite4java-linux-amd64'               : "com.almworks.sqlite4java:libsqlite4java-linux-amd64:${versions.sqlite4javaVersion}",
        'libsqlite4java-linux-i386'                : "com.almworks.sqlite4java:libsqlite4java-linux-i386:${versions.sqlite4javaVersion}",
        'libsqlite4java-osx'                       : "com.almworks.sqlite4java:libsqlite4java-osx:${versions.sqlite4javaVersion}",
        'logback-classic'                          : "ch.qos.logback:logback-classic:${versions.logbackClassicVersion}",
        'logback-core'                             : "ch.qos.logback:logback-core:${versions.logbackClassicVersion}",
        'logback-jackson'                          : "ch.qos.logback.contrib:logback-jackson:${versions.logbackContrib}",
        'logback-json-classic'                     : "ch.qos.logback.contrib:logback-json-classic:${versions.logbackContrib}",
        'log4j-core'                               : "org.apache.logging.log4j:log4j-core:${versions.apacheLog4j}",
        'log4j-api'                                : "org.apache.logging.log4j:log4j-api:${versions.apacheLog4j}",
        'mockito-core'                             : "org.mockito:mockito-core:${versions.mockitoVersion}",
        'mockito-junit-jupiter'                    : "org.mockito:mockito-junit-jupiter:${versions.mockitoJunitJupiterVersion}",
        'nova-common'                              : "com.loyalty.nova:nova-common:${versions.novaCommonVersion}",
        'nova-common-events'                       : "com.loyalty.nova:nova-common-events:${versions.novaCommonEventsVersion}",
        'nova-common-test'                         : "com.loyalty.nova:nova-common-test:${versions.novaCommonTestVersion}",
        'netty-handler'                            : "io.netty:netty-handler:${versions.nettyVersion}",
        'netty-codec'                              : "io.netty:netty-codec:${versions.nettyVersion}",
        'netty-codec-http'                         : "io.netty:netty-codec-http:${versions.nettyHttpVersion}",
        'netty-codec-http2'                        : "io.netty:netty-codec-http2:${versions.nettyVersion}",
        'netty-common'                             : "io.netty:netty-common:${versions.nettyVersion}",
        'netty-buffer'                             : "io.netty:netty-buffer:${versions.nettyVersion}",
        'netty-resolver'                           : "io.netty:netty-resolver:${versions.nettyVersion}",
        'netty-transport'                          : "io.netty:netty-transport:${versions.nettyVersion}",
        'netty-transport-native-epoll'             : "io.netty:netty-transport-native-epoll:${versions.nettyVersion}",
        'netty-transport-native-unix-common'       : "io.netty:netty-transport-native-unix-common:${versions.nettyVersion}",
        'postgresql'                               : "org.postgresql:postgresql:${versions.postgresqlVersion}",
        'protobuf-java'                            : "com.google.protobuf:protobuf-java:${versions.protobufJavaVersion}",
        'reactor-kotlin-extensions'                : "io.projectreactor.kotlin:reactor-kotlin-extensions:${versions.reactorKotlinExtensionsVersion}",
        'snakeyaml'                                : "org.yaml:snakeyaml:${versions.snakeYamlVersion}",
        'spring-mock'                              : "org.springframework:spring-mock:${versions.springMockVersion}",
        'springfox-swagger-ui'                     : "io.springfox:springfox-swagger-ui:${versions.swaggerVersion}",
        'springfox-swagger2'                       : "io.springfox:springfox-swagger2:${versions.swaggerVersion}",
        'sqlite4java'                              : "com.almworks.sqlite4java:sqlite4java:${versions.sqlite4javaVersion}",
        'sqlite4java-win32-x64'                    : "com.almworks.sqlite4java:sqlite4java-win32-x64:${versions.sqlite4javaVersion}",
        'sqlite4java-win32-x86'                    : "com.almworks.sqlite4java:sqlite4java-win32-x86:${versions.sqlite4javaVersion}",
        'spring-cloud-function-adapter'            : "org.springframework.cloud:spring-cloud-function-adapter-aws:${versions.springContextVersion}",
        'spring-cloud-function-core'               : "org.springframework.cloud:spring-cloud-function-core:${versions.springContextVersion}",
        'spring-cloud-function-web'                : "org.springframework.cloud:spring-cloud-function-web:${versions.springContextVersion}",
        'spring-cloud-started-function-web'        : "org.springframework.cloud:spring-cloud-started-function-web:${versions.springContextVersion}",
        'spring-cloud-function-context'            : "org.springframework.cloud:spring-cloud-function-context:${versions.springContextVersion}",
        'spring-cloud-function-adapter-aws'        : "org.springframework.cloud:spring-cloud-function-adapter-aws:${versions.springContextVersion}",
        'spring-cloud-function-kotlin'             : "org.springframework.cloud:spring-cloud-function-kotlin:${versions.springContextVersion}",
        'spring-cloud-starter-function-web'        : "org.springframework.cloud:spring-cloud-starter-function-web:${versions.springContextVersion}",
        'spring-cloud-starter-stream-kafka'        : "org.springframework.cloud:spring-cloud-starter-stream-kafka:${versions.springCloudKafkaVersion}",
        'spring-cloud-starter-stream'              : "org.springframework.cloud:spring-cloud-stream:${versions.springCloudKafkaVersion}",
        'spring-cloud-starter-binder'              : "org.springframework.cloud:spring-cloud-stream-binder-kafka:${versions.springCloudKafkaVersion}",
        'spring-cloud-starter-binder-core'         : "org.springframework.cloud:spring-cloud-stream-binder-kafka-core:${versions.springCloudKafkaVersion}",
        'typesafe-config'                          : "com.typesafe:config:${versions.typesafeConfigVersion}",
        'wiremock-jre8'                            : "com.github.tomakehurst:wiremock-jre8:${versions.wiremockVersion}"
]
