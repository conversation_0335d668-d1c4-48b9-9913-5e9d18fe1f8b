import groovy.json.JsonOutput

/**
 * Hosts the common utility functions for j<PERSON>kins pipelines
 */

/**
 * Get the CFN stack name
 * @param env [environment name]
 * @return [CFN stack name wrt environment name]
 */
String getCFNStackName(String env) {
    return "${env}-nova-offer-state-writer"
}

/**
 * get the name of the repository
 * @return [Name of the repository]
 */
String getRepoName() {
    return scm.getUserRemoteConfigs()[0].getUrl().tokenize('/').last().split("\\.")[0]
}

void deployResourceEventBusStack(String env) {
    echo "Deploying Resource Stack ${env}"
    def stackName = "${getCFNStackName(env)}-eventbus"
    def tags = paramsFromKeyValuePairsFromFile("cfn/${env}.tags.json")
    def parameterOverrides = paramsFromFile("cfn/${env}.params.json")
    def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file cfn/templates/resource_kinesis.yaml --parameter-overrides ${parameterOverrides} --tags ${tags} --capabilities CAPABILITY_IAM --no-fail-on-empty-changeset"
    sh "aws cloudformation ${args}"
    sh "aws cloudformation update-termination-protection --enable-termination-protection --stack-name ${stackName} --region us-east-1"
    sh "aws cloudformation detect-stack-drift --stack-name ${stackName} --region us-east-1"
}

void deployProducerStack(String env, String version, String bucketName = 'dev-amrpwl-lambda-functions-archive') {
    echo "Deploy Lambda Stack ${env}"
    def stackName = "${getCFNStackName(env)}-producer-lambda"
    def tags = paramsFromKeyValuePairsFromFile("cfn/${env}.tags.json")
    def template = readYaml file: 'cfn/templates/producer-template.yaml'
    template.Resources.StateProducerLambda.Properties.CodeUri = "./../nova-offer-state-writer-producer/build/distributions/nova-offer-state-writer-producer-${version}.zip"
    writeYaml file: "build/${env}-producer-template.yaml", data: template
    def parameterOverrides = paramsFromFile("cfn/${env}.params.json")
    def argsPackage = "package --template-file build/${env}-producer-template.yaml --s3-bucket ${bucketName} --s3-prefix sam/${env}-nova-offer-state-writer-producer-lambda-${version} --output-template-file build/packaged-${env}-producer-template.yaml"
    sh "aws cloudformation ${argsPackage}"
    def argsDeploy = "deploy --template-file build/packaged-${env}-producer-template.yaml --region us-east-1 --stack-name ${stackName} --parameter-overrides ${parameterOverrides} --tags ${tags} --capabilities CAPABILITY_NAMED_IAM --no-fail-on-empty-changeset"
    sh "aws cloudformation ${argsDeploy}"
    sh "aws cloudformation detect-stack-drift --stack-name ${stackName} --region us-east-1"
}

/**
 * Read json file and return as String required by aws cli
 * @param filename [Name of the file]
 * @param [ 'ParameterKey'   [Key ]
 * @param 'ParameterValue'] [Value]
 * @return [String of key values pairs]
 */
def paramsFromFile(String filename, keyPair = ['ParameterKey', 'ParameterValue']) {
    assert keyPair.size() == 2

    def paramsJson = readJSON(file: filename)

    paramsJson.collect { item ->
        keyPair.collect { key ->
            item.get(key)
        }.join('=')
    }.join(' ')

}


def paramsFromKeyValuePairsFromFile(String filename) {
    def paramsJson = readJSON(file: filename)
    paramsJson.collect {
        item -> "${item}"
    }.join(" ")
}

/**
 * Get artifact from jfrog
 * @param location location in jfrog
 * @param target target folder
 */
void copyArtifactFromJfrog(String location, String target) {
    rtDownload(
            serverId: "jfrog-artifactory",
            spec:
                    """{
        "files": [{
            "pattern": "libs-release-local/${location}",
            "target": "${target}/"
          }]
      }"""
    )
}

return this
