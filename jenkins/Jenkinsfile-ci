@Library('jenkins-shared-lib-v2') _

config = {}
jenkinsUtils = null

def server = Artifactory.server 'jfrog-artifactory'
def rtGradle = Artifactory.newGradleBuild()
rtGradle.resolver repo: 'jcenter', server: server
rtGradle.useWrapper = true
rtGradle.usesPlugin = true
rtGradle.deployer.deployArtifacts = true
rtGradle.deployer.deployMavenDescriptors = true
rtGradle.deployer.mavenCompatible = true
def buildInfo = Artifactory.newBuildInfo()

/**
 * Checks if the current commit is from jenkins
 * @return true when the current commit is from jenkins
 */
boolean isJenkinsCommit() {
    def commitEmail = sh(script: "git log -1 --pretty=format:'%ae'", returnStdout: true)?.trim()
    return (commitEmail == "${env.GIT_COMMITTER_EMAIL}")
}

/**
 * Check if the current branch is master
 * @return true when the current branch is master
 */
boolean isMaster() {
    def branchFullName = "${env.GIT_BRANCH}"
    def branchList = branchFullName.tokenize('/')
    def branchName = branchList.get(branchList.size() - 1)
    return branchName == 'master'
}

pipeline {
    agent {
        docker {
             image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
         }
    }
    stages {


        stage("Repository Information") {
            steps {
                println "Repository Information"
                script {
                    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
                    env.SKIP_SNAPSHOT_RELEASE = (!isJenkinsCommit() && isMaster() && true)
                    config.repoName = jenkinsUtils.getRepoName()
                }
            }
        }
        stage("Build") {
            steps {
                println "Build"
                sh(script: './gradlew build')
                withCredentials([[
                                         $class       : "AmazonWebServicesCredentialsBinding",
                                         credentialsId: "dev-amrpwl-aws-deployer"
                                 ]]) {
                    sh(script: './jenkins/validate-cfn')
                }
                script {
                    config.versionSnapshot = sh(script: './gradlew properties -q | grep ^version: | awk \'{print $2}\'', returnStdout: true)?.trim()
                    def (versionNumber, snapshot) = config.versionSnapshot.tokenize('-')
                    config.version = versionNumber
                }
            }
            post {
                always {
                    script {
                        gitUtils.reportPRStatus()
                    }
                }
            }
        }

        stage("Checkmarx Scan") {
          when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
          steps {
            step([$class: 'CxScanBuilder',
              preset: '36', exclusionsSetting: 'global', failBuildOnNewResults: "${!isMaster()}", vulnerabilityThresholdEnabled: true, excludeFolders: 'node_modules/**',
              failBuildOnNewSeverity: 'HIGH', jobStatusOnError: 'FAILURE', waitForResultsEnabled: true, avoidDuplicateProjectScans: true,
              filterPattern: '''!**/_cvs/**/*, !**/.svn/**/*,   !**/.hg/**/*,   !**/.git/**/*,  !**/.bzr/**/*, !**/bin/**/*,
              !**/build/**/*, !**/target/**/*, !**/.gradle/**/*, !**/obj/**/*,  !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws,
              !**/*.bak,     !**/*.tmp,       !**/*.aac,      !**/*.aif,      !**/*.iff,     !**/*.m3u, !**/*.mid, !**/*.mp3,
              !**/*.mpa,     !**/*.ra,        !**/*.wav,      !**/*.wma,      !**/*.3g2,     !**/*.3gp, !**/*.asf, !**/*.asx,
              !**/*.avi,     !**/*.flv,       !**/*.mov,      !**/*.mp4,      !**/*.mpg,     !**/*.rm,  !**/*.swf, !**/*.vob,
              !**/*.wmv,     !**/*.bmp,       !**/*.gif,      !**/*.jpg,      !**/*.png,     !**/*.psd, !**/*.tif, !**/*.swf,
              !**/*.jar,     !**/*.zip,       !**/*.rar,      !**/*.exe,      !**/*.dll,     !**/*.pdb, !**/*.7z,  !**/*.gz,
              !**/*.tar.gz,  !**/*.tar,       !**/*.gz,       !**/*.ahtm,     !**/*.ahtml,   !**/*.fhtml, !**/*.hdm,
              !**/*.md,      !**/*.json,      !**/*.xml,      !**/*.properties,  !**/*.gradle, !**/*.csv,
              !**/*.hdml,    !**/*.hsql,      !**/*.ht,       !**/*.hta,      !**/*.htc,     !**/*.htd, !**/*.war, !**/*.ear,
              !**/*.htmls,   !**/*.ihtml,     !**/*.mht,      !**/*.mhtm,     !**/*.mhtml,   !**/*.ssi, !**/*.stm,
              !**/*.stml,    !**/*.ttml,      !**/*.txn,      !**/*.xhtm,     !**/*.xhtml,   !**/*.class, !**/*.iml,
              !Checkmarx/Reports/*.*''', fullScanCycle: 50, groupId: '19431a46-4d94-4130-9a78-b5e2833244c6',
              osaArchiveIncludePatterns: '*.zip, *.war, *.ear, *.tgz', osaInstallBeforeScan: false,
              projectName: "${config.repoName}", sastEnabled: true, generateXmlReport: true,
              highThreshold: 0, mediumThreshold: null, lowThreshold: null,
              serverUrl: 'https://checkmarx.loyalty.com', sourceEncoding: '1'])
          }
        }

        stage("Sole Deploy") {
            when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
            steps {
                println "Sole Deploy"
                withCredentials([[
                                         $class       : "AmazonWebServicesCredentialsBinding",
                                         credentialsId: "dev-amrpwl-aws-deployer"
                                 ]]) {
                    // deployJenkinsAccessToResourceStack()
                    script {
                        jenkinsUtils.deployResourceEventBusStack('sole')
                        jenkinsUtils.deployProducerStack('sole', config.versionSnapshot)
                    }
                }
            }
        }
        stage("Sole Test") {
            when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
            steps {
                println "Sole Test"
                withCredentials([[
                                         $class       : "AmazonWebServicesCredentialsBinding",
                                         credentialsId: "dev-amrpwl-aws-deployer"
                                 ]]) {
                    sh(script: './gradlew integrationTest -PcucumberTag=@sole -Pprofiles=dev')
                }
            }
            post {
                always {
                    cucumber buildStatus: null,
                            fileIncludePattern: '**/build/reports/cucumber/sole-cucumber-json-report.json',
                            trendsLimit: 10
                }

                failure {
                    echo "Failed Sole Test"
                    withCredentials([[
                                             $class       : "AmazonWebServicesCredentialsBinding",
                                             credentialsId: "dev-amrpwl-aws-deployer"
                                     ]]) {
                        script {
                            jenkinsUtils.deleteStack()
                        }
                    }
                }
            }
        }

        stage("Release") {
            when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
            steps {
                println "Tag Release"
                git(
                        url: "**************:AirMilesLoyaltyInc/nova-offer-state-writer.git",
                        branch: "master",
                        credentialsId: "jenkins-ssh-key"
                )
                sshagent(credentials: ['jenkins-ssh-key'], ignoreMissing: false) {
                    sh(script: './gradlew release -Prelease.useAutomaticVersion=true')
                }
            }
            post {
                success {
                    echo 'success! Lets start up the deployment job.'
                    build job: 'Deployment/nova-offer-state-writer', parameters: [[$class: 'StringParameterValue', name: 'BUILD_VERSION', value: "${config.version}"]], wait: false
                }
                failure {
                    echo "failure occurred."
                }
                aborted {
                    echo "job aborted."
                }
            }
        }
    }
}
