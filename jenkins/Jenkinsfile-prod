def blue = "#42b3f4"
def good = "#3dd62f"
def danger = "#f45641"
def warning = "#ffd344"

jenkinsUtils = null

void importFunctions() {
    jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
}

pipeline {
    agent none
    stages {
        stage('Deploying to Production') {
            agent {
                docker {
                    image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                }
            }

            steps {
                println "Deploying to Production"
                println "BUILD_VERSION: ${params.BUILD_VERSION}"
                importFunctions()
                withCredentials([[$class: "AmazonWebServicesCredentialsBinding", credentialsId: "prod-amrpwl-aws-deployer"]]) {
                    script {
                        sh(script: './gradlew build')
                        jenkinsUtils.deployProducerStack('prod', params.BUILD_VERSION, 'prod-amrpwl-lambda-functions-archive')
                    }
                }
            }

            post {
                success {
                    script {
                        echo 'success! Deployed to production'
                    }
                }
                failure {
                    script {
                        echo "failed to deploy to production"
                    }
                }
                aborted {
                    script {
                        echo "job aborted. Did not deploy to production"
                    }
                }
            }
        }
    }
}
