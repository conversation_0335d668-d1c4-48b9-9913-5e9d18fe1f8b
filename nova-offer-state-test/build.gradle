import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'

dependencies {
    implementation(
            'com.amazonaws:amazon-kinesis-deaggregator',
            'com.amazonaws:aws-lambda-java-log4j2',
            'org.springframework.cloud:spring-cloud-function-adapter-aws',
            'org.springframework.cloud:spring-cloud-function-core',
            'org.springframework.cloud:spring-cloud-function-web',
            'org.apache.httpcomponents:httpmime',
            'ch.qos.logback.contrib:logback-jackson',
            'ch.qos.logback.contrib:logback-json-classic',
            'ch.qos.logback:logback-classic',
            'com.amazonaws:aws-java-sdk-core',
            'com.amazonaws:DynamoDBLocal',
            'com.fasterxml.jackson.core:jackson-annotations',
            'com.fasterxml.jackson.core:jackson-databind',
            'com.fasterxml.jackson.module:jackson-module-kotlin',
            'com.github.tomakehurst:wiremock-jre8',
            'com.google.guava:guava',
            'com.h2database:h2',
            'com.icegreen:greenmail',
            'com.loyalty.nova:nova-common',
            'com.loyalty.nova:nova-common-events',
            'com.loyalty.nova:nova-common-test',
            'com.willowtreeapps.assertk:assertk-jvm',
            'io.confluent:kafka-avro-serializer',
            'io.cucumber:cucumber-java8',
            'io.cucumber:cucumber-junit',
            'io.cucumber:cucumber-spring',
            'io.kotlintest:kotlintest-extensions-spring',
            'com.loyalty.nova:nova-common',
            'com.loyalty.nova:nova-common-events',
            'org.apache.commons:commons-lang3',
            'org.apache.httpcomponents:httpclient',
            'org.apache.httpcomponents:httpmime',
            'org.apache.jmeter:ApacheJMeter_core',
            'org.hamcrest:java-hamcrest',
            'org.jetbrains.kotlin:kotlin-compiler-embeddable',
            'org.jetbrains.kotlin:kotlin-reflect',
            'org.jetbrains.kotlin:kotlin-script-runtime',
            'org.jetbrains.kotlin:kotlin-script-util',
            'org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.jetbrains.kotlin:kotlin-test',
            'org.mockito:mockito-core',
            'org.postgresql:postgresql',
            'org.springframework.boot:spring-boot-starter-data-jpa',
            'org.springframework.boot:spring-boot-starter-data-rest',
            'org.springframework.boot:spring-boot-starter-mail',
            'org.springframework.boot:spring-boot-starter-test',
            'org.springframework.cloud:spring-cloud-starter-stream-kafka',
            'org.springframework.integration:spring-integration-mail',
            'org.springframework.cloud:spring-cloud-function-context',
            'org.springframework:spring-context',
            'com.amazonaws:aws-java-sdk-kinesis',
            'org.xerial.snappy:snappy-java'
    )
}

sourceSets {
    integration {
        compileClasspath += sourceSets.main.output
        runtimeClasspath += sourceSets.main.output
    }
}

configurations {
    integrationImplementation.extendsFrom implementation
    integrationRuntimeOnly.extendsFrom runtime
    cucumberRuntime.extendsFrom runtime
}

task integrationTest(type: Test) {
    systemProperty 'spring.profiles.active', profiles
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
        excludeEngines 'junit-vintage'
    }
    testClassesDirs = sourceSets.integration.output.classesDirs
    classpath = sourceSets.integration.runtimeClasspath
    testLogging {
        // set options for log level LIFECYCLE
        events TestLogEvent.FAILED,
                TestLogEvent.PASSED,
                TestLogEvent.SKIPPED,
                TestLogEvent.STANDARD_OUT
        exceptionFormat TestExceptionFormat.FULL
        showExceptions true
        showCauses true
        showStackTraces true
        showStandardStreams true

        info.events = debug.events
        info.exceptionFormat = debug.exceptionFormat

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}

compileIntegrationKotlin {
    kotlinOptions {
        freeCompilerArgs = ["-Xjsr305=strict"]
        jvmTarget = JavaVersion.VERSION_11.toString()
    }
}
