package com.loyalty.nova.offer.state.writer.test

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isInstanceOf
import com.loyalty.nova.common.events.definitions.data.v3.StateChangedEventData
import com.loyalty.nova.common.test.runner.TabularSource
import com.loyalty.nova.offer.state.writer.test.event.EventSnooper
import org.junit.Assume
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.condition.EnabledIfSystemProperty
import org.junit.jupiter.params.ParameterizedTest
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.TestPropertySource
import org.springframework.test.context.junit4.SpringRunner
import java.util.UUID
import kotlin.test.assertEquals

@Tag("it")
@RunWith(SpringRunner::class)
@TestPropertySource(locations = ["classpath:application.yml"])
@EnableAutoConfiguration
@SpringBootTest(classes = [IntegrationConfig::class, EventSnooper::class])
@DisplayName("Verify State Event functionality")
class OfferStateChangedEventTest : TestBase() {

    @Autowired
    lateinit var eventSnooper: EventSnooper

    companion object {
        @BeforeAll
        @JvmStatic
        fun beforeAll(applicationContext: ApplicationContext) {
            val profilesFromConsole = System.getProperty("spring.profiles.active", "")
            Assume.assumeFalse(!profilesFromConsole.contains("local"))
            TestBase.beforeAll(applicationContext)
        }
    }

    @Test
    @EnabledIfSystemProperty(named = "spring.profiles.active", matches = "local")
    fun `verify check in events`() {
        val offer1Id = UUID.randomUUID()
        val offer2Id = UUID.randomUUID()
        eventSnooper.clear()
        httpRequest("/requests/OfferStateChangedEvent.json")
                .withTransformation(
                        "$.schema" to schema,
                        "$.host" to host,
                        "$.port" to port,
                        "$.path" to "/state-writer/v1/84085517999/put",
                        "$.body.stateChanges[0].offerId" to offer1Id.toString(),
                        "$.body.stateChanges[1].offerId" to offer2Id.toString()
                )
                .send {
                    Thread.sleep(2000L)
                    assertEquals(204, statusCode.value())
                    val eventData1 = eventSnooper.getEvents()[0].data
                    assertThat(eventData1).isInstanceOf(StateChangedEventData::class).also {
                        with(eventData1 as StateChangedEventData) {
                            assertThat(collectorId).isEqualTo("84085517999")
                            assertThat(name).isEqualTo("LOAD")
                            assertThat(value).isEqualTo("LOADED")
                            assertThat(offerId).isEqualTo(offer1Id)
                        }
                    }
                    val eventData2 = eventSnooper.getEvents()[1].data
                    assertThat(eventData2).isInstanceOf(StateChangedEventData::class).also {
                        with(eventData2 as StateChangedEventData) {
                            assertThat(collectorId).isEqualTo("84085517999")
                            assertThat(name).isEqualTo("LOAD")
                            assertThat(value).isEqualTo("UNLOADED")
                            assertThat(offerId).isEqualTo(offer2Id)
                        }
                    }
                }
    }

    @ParameterizedTest
    @TabularSource("""
     | invalid state name   | test | loaded  |400  | 84085517999 |
     | emplty state name    |      | loaded  |400  | 84085517999 |
     | invalid state value  | load  | test   |400  | 84085517999 |
     | empty state value    | load  |        |400  | 84085517999 |
     | invalid collectorId  | load  | loaded |400  | 1234        |
     | empty collectorId    | load  | loaded |405  |             |
        """)
    @EnabledIfSystemProperty(named = "spring.profiles.active", matches = "local")
    fun `verify validations for the writer`( testCase:String, stateName: String, stateValue: String, statusValue: Int, collectorId: String) {
        val offer1Id = UUID.randomUUID()
        val offer2Id = UUID.randomUUID()
        eventSnooper.clear()

        httpRequest("/requests/OfferStateChangedEvent.json")
                .withTransformation(
                        "$.schema" to schema,
                        "$.host" to host,
                        "$.port" to port,
                        "$.path" to "/state-writer/v1/$collectorId/put",
                        "$.body.stateChanges[0].offerId" to offer1Id.toString(),
                        "$.body.stateChanges[1].offerId" to offer2Id.toString(),
                        "$.body.stateChanges[0].states[0].name" to stateName,
                        "$.body.stateChanges[0].states[0].value" to stateValue
                )
                .send {
                    assertEquals(statusValue, statusCode.value())
                }
    }
}
