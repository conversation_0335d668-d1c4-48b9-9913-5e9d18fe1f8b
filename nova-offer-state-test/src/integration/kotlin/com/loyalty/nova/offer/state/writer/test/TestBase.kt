package com.loyalty.nova.offer.state.writer.test

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.test.runner.HttpRequestBuilder
import org.junit.Assume
import org.springframework.context.ApplicationContext

abstract class TestBase {

    companion object {

        lateinit var objectMapper: ObjectMapper

        lateinit var context: TestContext

        lateinit var schema: String

        lateinit var host: String

        lateinit var port: String

        fun beforeAll(applicationContext: ApplicationContext) {
            val profilesFromConsole = System.getProperty("spring.profiles.active", "")
            Assume.assumeFalse(profilesFromConsole.contains("prod"))

            objectMapper = applicationContext.getBean(ObjectMapper::class.java)
            context = TestContext(applicationContext = applicationContext)
            schema = context.environment.getProperty("writer.schema")!!
            host = context.environment.getProperty("writer.host")!!
            port = context.environment.getProperty("writer.port")!!
        }
    }
}

fun httpRequest(requestJsonFileName: String): HttpRequestBuilder {
    return HttpRequestBuilder(TestBase.objectMapper, requestJsonFileName)
}
