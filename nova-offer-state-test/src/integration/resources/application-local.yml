spring:
  main:
    allow-bean-definition-overriding: true
  cloud:
    stream:
      binders:
        kafka:
          type: kafka
      kafka:
        binder:
          headers: eventType
        bindings:
          offer-event-bus-input:
            consumer:
              configuration:
                key:
                  serializer: org.apache.kafka.common.serialization.StringSerializer
      inMemory:
        binder:
          headers: eventType
      bindings:
        offer-event-bus-input:
          destination: events-local
          binder: kafka
          group: integrationTest
      schema-registry-client:
        endpoint: http://localhost:8081
writer:
  schema: http
  host: localhost
  port: 2992

