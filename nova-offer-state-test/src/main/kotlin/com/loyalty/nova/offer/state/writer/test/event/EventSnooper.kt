package com.loyalty.nova.offer.state.writer.test.event

import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.EventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import com.loyalty.nova.common.logging.logger
import com.loyalty.nova.offer.state.writer.test.OfferEventBusInput
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.stream.annotation.EnableBinding
import org.springframework.cloud.stream.annotation.StreamListener
import java.util.Collections

@EnableBinding(OfferEventBusInput::class)
class EventSnooper() {

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    private val eventBuffer = mutableListOf<Event<EventData, EventMeta>>()

    @StreamListener(OfferEventBusInput.INPUT)
    fun handleEvent(event: Event<EventData, EventMeta>) {
        logger.info("\n<==== ${objectMapper.writeValueAsString(event)}")
        eventBuffer.add(event)
    }

    fun getEvents(): List<Event<EventData, EventMeta>> {
        return Collections.unmodifiableList(eventBuffer)
    }

    fun clear() {
        eventBuffer.clear()
    }
}
