{"body": "{\"stateChanges\":[{\"offerId\":\"d72fc41e-4fc2-4d93-bf81-539c63358db8\",\"states\":[{\"name\":\"load\",\"value\":\"loaded\"},{\"name\":\"like\",\"value\": \"liked\"}]}]}", "path": "/state-writer/v1/84085517357/put", "httpMethod": "PUT", "isBase64Encoded": false, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "multiValueHeaders": {"Accept": ["application/json"], "Content-Type": ["application/json"]}, "requestContext": {"identity": {}}}