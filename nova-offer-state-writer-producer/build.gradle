apply plugin: 'kotlin-spring'
apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'org.springframework.boot'
apply plugin: 'kotlin-kapt'

dependencies {
    implementation(
            'com.amazonaws.serverless:aws-serverless-java-container-springboot2',
            'com.amazonaws:aws-java-sdk-core',
            'com.amazonaws:aws-java-sdk-lambda',
            'com.amazonaws:aws-lambda-java-core',
            'com.fasterxml.jackson.core:jackson-annotations',
            'com.fasterxml.jackson.core:jackson-core',
            'com.fasterxml.jackson.core:jackson-databind',
            'com.fasterxml.jackson.module:jackson-module-kotlin',
            'com.loyalty.nova:nova-common',
            'com.loyalty.nova:nova-common-events',
            'io.springfox:springfox-swagger-ui',
            'io.springfox:springfox-swagger2',
            'org.hibernate.validator:hibernate-validator',
            'org.jetbrains.kotlin:kotlin-noarg',
            'org.jetbrains.kotlin:kotlin-reflect',
            'org.jetbrains.kotlin:kotlin-stdlib-jdk8',
            'org.springframework.boot:spring-boot-starter-web',
            'org.springframework.cloud:spring-cloud-starter-function-web',
            'org.springframework.cloud:spring-cloud-starter-stream-kafka',
            'software.amazon.kinesis:amazon-kinesis-client',
            'ch.qos.logback.contrib:logback-jackson',
            'ch.qos.logback.contrib:logback-json-classic',
    )
    testImplementation(
            'org.hamcrest:java-hamcrest',
            'org.jetbrains.kotlin:kotlin-test',
            'org.springframework.boot:spring-boot-starter-aop',
            'org.springframework.boot:spring-boot-starter-test',
    )
}

task buildZip(type: Zip) {
    from compileKotlin
    from processResources
    into('lib') {
        from(configurations.compileClasspath) {
            exclude('tomcat-embed-*', '*kafka*', 'spring-cloud-stream-*')

        }
    }
}

bootJar.enabled = false
jar.enabled = true

build.dependsOn buildZip
