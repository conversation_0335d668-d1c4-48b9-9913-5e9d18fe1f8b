package com.loyalty.nova.offer.state.writer.producer

import com.loyalty.nova.common.logging.logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationFailedEvent
import org.springframework.context.ApplicationListener
import org.springframework.context.annotation.Configuration

@Configuration
class BeansAuditUtils : ApplicationListener<ApplicationFailedEvent> {

    @Value("\${spring.profiles.active}")
    private val activeProfile: String? = null

    override fun onApplicationEvent(event: ApplicationFailedEvent) {
        if (activeProfile != "prod") {
            val buffer: StringBuffer = StringBuffer("============ beans ============\n")
            event.applicationContext.beanDefinitionNames.forEach { buffer.append("$it \n") }
            buffer.append("===========================================\n")
            logger.info("Environment :\n$buffer")
        }
    }
}
