package com.loyalty.nova.offer.state.writer.producer

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.services.kinesis.AmazonKinesisClientBuilder
import com.amazonaws.services.kinesis.model.PutRecordsRequest
import com.amazonaws.services.kinesis.model.PutRecordsRequestEntry
import com.fasterxml.jackson.databind.ObjectMapper
import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.StateChangedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.nio.ByteBuffer

@Profile("!local")
@Service
class EventProducerServiceKinesisClientImpl : EventProducerService {

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Value("\${state.writer.event.stream.name:}")
    lateinit var stateWriterEventStreamName: String

    override fun publishEvent(event: Event<StateChangedEventData, EventMeta>) {

        val kinesisClient = AmazonKinesisClientBuilder.standard()
                .withCredentials(DefaultAWSCredentialsProviderChain())
                .build()

        val putRecordsRequest = PutRecordsRequest()
        putRecordsRequest.setStreamName(stateWriterEventStreamName)
        val putRecordsRequestEntry = PutRecordsRequestEntry()
                .withData(ByteBuffer.wrap(objectMapper.writeValueAsBytes(event)))
                .withPartitionKey(event.meta.partitionKey)

        putRecordsRequest.setRecords(listOf(putRecordsRequestEntry))
        kinesisClient.putRecords(putRecordsRequest)
    }

}
