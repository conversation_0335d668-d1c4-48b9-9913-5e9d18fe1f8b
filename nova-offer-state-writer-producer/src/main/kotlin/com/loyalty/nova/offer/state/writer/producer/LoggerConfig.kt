package com.loyalty.nova.offer.state.writer.producer

import com.loyalty.nova.offer.state.writer.producer.utils.AccessLogEnhanceFilter
import com.loyalty.nova.offer.state.writer.producer.utils.MdcLogEnhanceFilter
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.InjectionPoint
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope

@Configuration
class LoggerConfig {

    @Bean
    fun loggingFilter(): FilterRegistrationBean<MdcLogEnhanceFilter> {
        val registrationBean = FilterRegistrationBean<MdcLogEnhanceFilter>()
        registrationBean.order = Integer.MAX_VALUE - 1
        registrationBean.filter = MdcLogEnhanceFilter()
        return registrationBean
    }

    @Bean
    fun accessLogging(): FilterRegistrationBean<AccessLogEnhanceFilter> {
        val registrationBean = FilterRegistrationBean<AccessLogEnhanceFilter>()
        registrationBean.filter = AccessLogEnhanceFilter()
        return registrationBean
    }

    @Bean
    @Scope(value = "prototype")
    fun exposeLogger(injectionPoint: InjectionPoint): Logger =
            LoggerFactory.getLogger(injectionPoint.member.declaringClass.name)
}

