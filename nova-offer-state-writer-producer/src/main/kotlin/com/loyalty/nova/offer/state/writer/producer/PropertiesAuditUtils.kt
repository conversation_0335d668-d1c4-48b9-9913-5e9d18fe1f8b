package com.loyalty.nova.offer.state.writer.producer

import com.loyalty.nova.common.logging.logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationContextInitializedEvent
import org.springframework.context.ApplicationListener
import org.springframework.core.env.AbstractEnvironment
import org.springframework.core.env.EnumerablePropertySource
import org.springframework.core.env.Environment
import java.util.Arrays
import java.util.stream.StreamSupport

class PropertiesAuditUtils : ApplicationListener<ApplicationContextInitializedEvent> {

    @Value("\${spring.profiles.active}")
    private val activeProfile: String? = null

    override fun onApplicationEvent(event: ApplicationContextInitializedEvent) {
        if (activeProfile != "prod") {
            val buffer: StringBuffer = StringBuffer("============ System Properties ============\n")

            val properties = System.getProperties()
            properties.forEach { k, v -> buffer.append("$k = $v\n") }

            buffer.append("============ env Properties ============\n")

            val env = System.getenv()
            env.forEach { k, v -> buffer.append("$k = $v\n") }

            buffer.append("============ configuration ============\n")
            val contextEnv: Environment = event.applicationContext.environment
            buffer.append("Active profiles: ", Arrays.toString(contextEnv.activeProfiles))
            buffer.append("\n")
            val sources = (contextEnv as AbstractEnvironment).propertySources
            StreamSupport.stream(sources.spliterator(), false)
                    .filter { ps -> ps is EnumerablePropertySource<*> }
                    .map { ps -> (ps as EnumerablePropertySource<*>).propertyNames }
                    .flatMap(Arrays::stream)
                    .distinct()
                    .forEach { prop -> buffer.append("$prop: ${getPropertyValue(prop, contextEnv)}\n") }
            buffer.append("===========================================\n")

            logger.info("Environment :\n$buffer")
        }
    }

    private fun getPropertyValue(name: String, contextEnv: Environment): String? {
        return if (name.contains("password")) {
            "*****"
        } else {
            contextEnv.getProperty(name)
        }
    }
}
