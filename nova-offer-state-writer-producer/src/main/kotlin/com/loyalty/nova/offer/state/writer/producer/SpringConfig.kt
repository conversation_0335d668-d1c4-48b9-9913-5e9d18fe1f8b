@file:Suppress(
        "UnusedPrivateMember"
)

package com.loyalty.nova.offer.state.writer.producer

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.springframework.beans.factory.annotation.Value
import org.springframework.beans.factory.config.ConfigurableBeanFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider

@Configuration
class SpringConfig {

    @Value("\${spring.profiles.active}")
    private val activeProfile: String? = null

    @Bean
    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    fun getCredentials(): DefaultCredentialsProvider = DefaultCredentialsProvider.create()

    @Bean
    fun getObjectMapper(): ObjectMapper {
        return jacksonObjectMapper()
                .findAndRegisterModules()
                .registerKotlinModule().registerModule(JavaTimeModule())
                .configure(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS, false)
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
    }
}
