package com.loyalty.nova.offer.state.writer.producer

import com.amazonaws.serverless.proxy.model.AwsProxyRequest
import com.amazonaws.serverless.proxy.model.AwsProxyResponse
import com.amazonaws.serverless.proxy.spring.SpringBootLambdaContainerHandler
import com.amazonaws.services.lambda.runtime.Context
import com.amazonaws.services.lambda.runtime.RequestStreamHandler
import com.loyalty.nova.offer.state.writer.ProducerApp
import java.io.InputStream
import java.io.OutputStream

class StreamLambdaHandler : RequestStreamHandler {

    private var handler: SpringBootLambdaContainerHandler<AwsProxyRequest, AwsProxyResponse> =
            SpringBootLambdaContainerHandler.getAwsProxyHandler(ProducerApp::class.java)

    override fun handleRequest(input: InputStream?, output: OutputStream?, context: Context?) {
        handler.proxyStream(input, output, context);
    }
}
