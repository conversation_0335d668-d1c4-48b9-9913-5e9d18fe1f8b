package com.loyalty.nova.offer.state.writer.producer

import com.loyalty.nova.offer.state.writer.producer.validation.DefaultStatePairValidator
import com.loyalty.nova.offer.state.writer.producer.validation.StatePairValidator
import org.springframework.beans.factory.config.BeanDefinition
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Scope

@Configuration
class ValidationConfig {

    @Bean
    @Primary
    @Scope(BeanDefinition.SCOPE_SINGLETON)
    fun getStatePairValidator(): StatePairValidator {
        return DefaultStatePairValidator(
                statePairMapping = mapOf(
                        "SAVE" to listOf(
                                "SAVED",
                                "UNSAVED"
                        ),
                        "LOAD" to listOf(
                                "LOADED",
                                "UNLOADED"
                        ),
                        "LIKE" to listOf(
                                "LIKED",
                                "UNLIKED"
                        ),
                        "OPT_IN" to listOf(
                                "OPTED_IN"
                        )
                )
        )
    }
}
