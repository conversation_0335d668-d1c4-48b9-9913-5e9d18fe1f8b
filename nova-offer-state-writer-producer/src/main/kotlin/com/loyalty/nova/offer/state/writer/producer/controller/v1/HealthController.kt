package com.loyalty.nova.offer.state.writer.producer.controller.v1

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@RestController
class HealthController {

    @GetMapping("/state-writer/health")
    fun health(): ResponseEntity<Any> {
        return ResponseEntity.ok(object {
            val timestamp: Instant = Instant.now()
        })
    }
}
