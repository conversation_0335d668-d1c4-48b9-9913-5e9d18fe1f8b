package com.loyalty.nova.offer.state.writer.producer.controller.v1

import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.StateChangedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import com.loyalty.nova.common.logging.logger
import com.loyalty.nova.offer.state.writer.producer.EventProducerServiceKinesisClientImpl
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Profile
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.util.UUID

@RestController
@Profile("!local")
class KeepAliveController {

    @Autowired
    lateinit var eventProducerServiceKinesisClientImpl: EventProducerServiceKinesisClientImpl

    @GetMapping("/keep-alive")
    @Suppress("MagicNumber")
    fun keepAlive(): ResponseEntity<Any> {
        Thread.sleep(200L)
//        val eventMeta = EventMeta(
//            correlationId = "keep-alive",
//            originClient = "keep-alive",
//            partitionKey = "keep-alive",
//            timestamp = Instant.now()
//        )
//        val eventData = StateChangedEventData(
//                collectorId="00000000001", offerId = UUID.randomUUID(), name = "KEEPALIVE", value = "KEEPALIVE"
//        )
//        eventProducerServiceKinesisClientImpl.createEvent(
//                Event(data = eventData, id = UUID.randomUUID(), meta = eventMeta)
//        )
        logger.info("keep-alive has been called")
        return ResponseEntity.ok(object {
            val timestamp: Instant = Instant.now()
        })
    }
}
