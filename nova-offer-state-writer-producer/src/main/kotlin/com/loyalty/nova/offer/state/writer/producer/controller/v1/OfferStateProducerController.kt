package com.loyalty.nova.offer.state.writer.producer.controller.v1

import com.loyalty.nova.common.events.definitions.Event
import com.loyalty.nova.common.events.definitions.data.v3.StateChangedEventData
import com.loyalty.nova.common.events.definitions.meta.v1.EventMeta
import com.loyalty.nova.offer.state.writer.producer.EventProducerService
import com.loyalty.nova.offer.state.writer.producer.controller.v1.dto.OfferStateChangeRequestDTO
import com.loyalty.nova.offer.state.writer.producer.validation.StatePairValidator
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.util.UUID
import javax.validation.ValidationException
import javax.validation.constraints.Pattern

@RestController
@RequestMapping("/state-writer/v1")
@Validated
class OfferStateProducerController(
        private val service: EventProducerService,
        private val statePairValidator: StatePairValidator
) {

    @PutMapping("/{collectorId}/put", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @Suppress("MagicNumber")
    fun setState(
            @PathVariable("collectorId")
            @Pattern(regexp = "^[0-9]{11}\$")
            collectorId: String,

            @RequestBody
            offerStateChangeRequest: OfferStateChangeRequestDTO,

            @RequestHeader(name = "X-Correlation-Id")
            correlationId: String,

            @RequestHeader(name = "X-Origin-Client")
            originClient: String
    ): ResponseEntity<Unit> {
        if (offerStateChangeRequest.stateChanges.isEmpty() || offerStateChangeRequest.stateChanges.size > 100) {
            throw ValidationException("Must provide between 1 and 100 stateChanges")
        }

        offerStateChangeRequest.stateChanges
                .forEach { offerStateChangeDTO ->
                    if (offerStateChangeDTO.states.isEmpty()) {
                        throw ValidationException("Must provide more than 0 states")
                    }
                }
        offerStateChangeRequest.stateChanges
                .groupBy { offerStateChangeDTO -> offerStateChangeDTO.offerId }
                .forEach { grouping ->
                    if (grouping.value.size > 1) {
                        throw ValidationException("You cannot pass the same offer ID multiple times")
                    }
                }
        offerStateChangeRequest.stateChanges
                .forEach { offerStateChangeDTO ->
                    val stateNameCounts = offerStateChangeDTO.states
                            .groupBy { offerStateDTO -> offerStateDTO.name }
                            .map { entry -> entry.value.size }
                    if (stateNameCounts.any { count -> count > 1 }) {
                        throw ValidationException(
                                "You cannot change the same state for a given offer multiple times in a single request"
                        )
                    }
                }

        this.transformToEventDataList(collectorId, offerStateChangeRequest)
                .map { eventData -> this.createEvent(eventData, correlationId, originClient) }
                .forEach(this.service::publishEvent)
        return ResponseEntity.noContent().build()
    }

    private fun createEvent(
            eventData: StateChangedEventData, correlationId: String, originClient: String
    ): Event<StateChangedEventData, EventMeta> {
        return Event(
                id = UUID.randomUUID(),
                meta = EventMeta(
                        correlationId = correlationId,
                        originClient = originClient,
                        partitionKey = eventData.collectorId + eventData.offerId + eventData.name,
                        timestamp = Instant.now()
                ),
                data = eventData
        )
    }

    private fun transformToEventDataList(
            collectorId: String, offerStateChangeRequestDTO: OfferStateChangeRequestDTO
    ): List<StateChangedEventData> {
        return offerStateChangeRequestDTO.stateChanges.flatMap { offerStateChangeDTO ->
            return@flatMap offerStateChangeDTO.states
                    .distinctBy { offerStateDTO -> offerStateDTO.name.toUpperCase() }
                    .onEach { offerStateDTO ->
                        this.statePairValidator.validateStatePair(offerStateDTO.name, offerStateDTO.value)
                    }
                    .map { offerStateDTO ->
                        return@map StateChangedEventData(
                                collectorId = collectorId,
                                offerId = offerStateChangeDTO.offerId,
                                name = offerStateDTO.name.toUpperCase(),
                                value = offerStateDTO.value.toUpperCase(),
                                properties = offerStateDTO.properties
                        )
                    }
        }
    }
}
