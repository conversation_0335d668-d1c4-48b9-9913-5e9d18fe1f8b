package com.loyalty.nova.offer.state.writer.producer.exceptions

import org.springframework.beans.TypeMismatchException
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.lang.Nullable
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import org.springframework.web.util.WebUtils
import java.util.Date
import javax.validation.ConstraintViolationException
import javax.validation.ValidationException

@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
class HttpExceptionHandler : ResponseEntityExceptionHandler() {

    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(
            webRequest: WebRequest,
            e: ValidationException
    ): ResponseEntity<Any?> {
        return handleExceptionInternal(
                e, "Request contains non-parsable message", HttpHeaders(), HttpStatus.BAD_REQUEST, webRequest
        )
    }

    @ExceptionHandler(ValidationException::class)
    fun handleValidationException(
            webRequest: WebRequest,
            e: ValidationException
    ): ResponseEntity<Any?> {
        return handleExceptionInternal(
                e, "Request contains invalid parameters", HttpHeaders(), HttpStatus.BAD_REQUEST, webRequest
        )
    }

    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(
            webRequest: WebRequest,
            e: ConstraintViolationException
    ): ResponseEntity<Any?> {
        return handleExceptionInternal(
                e, "Request contains invalid parameters", HttpHeaders(), HttpStatus.BAD_REQUEST, webRequest
        )
    }

    override fun handleTypeMismatch(
            e: TypeMismatchException,
            headers: HttpHeaders,
            status: HttpStatus,
            request: WebRequest
    ): ResponseEntity<Any?> {
        return handleExceptionInternal(
                e, "Request contains invalid state query values", headers, status, request
        )
    }

    override fun handleMissingServletRequestParameter(
            e: MissingServletRequestParameterException,
            headers: HttpHeaders,
            status: HttpStatus,
            request: WebRequest
    ): ResponseEntity<Any?> {
        return handleExceptionInternal(
                e, "Request is missing mandatory query parameters", headers, status, request
        )
    }

    override fun handleExceptionInternal(
            e: Exception,
            @Nullable body: Any?,
            headers: HttpHeaders,
            status: HttpStatus,
            request: WebRequest
    ): ResponseEntity<Any?> {
        if (HttpStatus.INTERNAL_SERVER_ERROR == status) {
            request.setAttribute(WebUtils.ERROR_EXCEPTION_ATTRIBUTE, e, WebRequest.SCOPE_REQUEST)
        }
        val message = body?.toString() ?: "Internal server error"
        logger.error(message, e)
        val errorResponse = ErrorResponse(Date(), status, message, ErrorResponse.getCause(e).message)
        return ResponseEntity(errorResponse, headers, status)
    }
}
