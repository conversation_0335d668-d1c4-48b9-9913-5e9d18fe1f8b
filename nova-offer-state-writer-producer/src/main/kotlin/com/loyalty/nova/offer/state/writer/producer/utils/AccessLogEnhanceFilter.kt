package com.loyalty.nova.offer.state.writer.producer.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.loyalty.nova.common.logging.jsonInfo
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import javax.servlet.Filter
import javax.servlet.FilterChain
import javax.servlet.ServletRequest
import javax.servlet.ServletResponse
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

class AccessLogEnhanceFilter : Filter {
    private var logger: Logger = LoggerFactory.getLogger(this::class.java)

    override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
        chain.doFilter(request, response)

        if (request is HttpServletRequest && response is HttpServletResponse) {
            val mapper: ObjectMapper = jacksonObjectMapper()
            val auditAttrs = mapOf<String, Any>(
                    "audit" to true,
                    "userAgent" to request.getHeader("User-Agent"),
                    "remoteHost" to request.remoteHost,
                    "remoteUser" to request.remoteUser,
                    "originClient" to request.getHeader("x-origin-client"),
                    "correlationId" to request.getHeader("x-correlation-id"),
                    "method" to request.method,
                    "requestURI" to request.requestURI,
                    "queryString" to request.queryString,
                    "status" to response.status,
                    "bufferSize" to response.bufferSize
            )
            logger.jsonInfo("http audit", auditAttrs, mapper.disable(SerializationFeature.INDENT_OUTPUT))
        }
    }
}
