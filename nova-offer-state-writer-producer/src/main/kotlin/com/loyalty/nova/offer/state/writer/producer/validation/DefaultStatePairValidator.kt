package com.loyalty.nova.offer.state.writer.producer.validation

class DefaultStatePairValidator(
        statePairMapping: Map<String, List<String>>
) : StatePairValidator {

    private val statePairMapping = statePairMapping.entries
            .associate { entry -> entry.key.toUpperCase() to entry.value.map(String::toUpperCase) }

    override fun validateStatePair(stateName: String, stateValue: String) {
        try {
            val valid = this.statePairMapping
                    .getValue(stateName.toUpperCase())
                    .contains(stateValue.toUpperCase())
            if (!valid) {
                throw StatePairValidationException("State value '$stateValue' is not valid for state name '$stateName'")
            }
        } catch (exception: NoSuchElementException) {
            throw StatePairValidationException("State name '$stateName' is not valid")
        }
    }
}

