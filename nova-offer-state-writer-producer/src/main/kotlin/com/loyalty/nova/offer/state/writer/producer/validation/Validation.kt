package com.loyalty.nova.offer.state.writer.producer.validation

import javax.validation.ConstraintViolation
import javax.validation.ConstraintViolationException
import javax.validation.Validation
import javax.validation.ValidatorFactory

fun <TEntity> TEntity.validate(): Set<ConstraintViolation<TEntity>> {
    return Validation.buildDefaultValidatorFactory()
            .let(ValidatorFactory::getValidator)
            .validate(this)
}

fun <TEntity> TEntity.validateAndThrow() {
    this.validate().also { constraintViolations ->
        if (constraintViolations.isNotEmpty()) {
            throw ConstraintViolationException(constraintViolations)
        }
    }
}
