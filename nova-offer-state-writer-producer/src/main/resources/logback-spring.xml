<configuration>
    <springProfile name="!local">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="com.loyalty.nova.common.logging.NovaConsoleAppenderEncoder">
                <pattern>[%level] %date{ISO8601} %msg%n</pattern>
                <layout class="com.loyalty.nova.common.logging.NovaLogbackJsonLayout">
                    <jsonFormatter
                            class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter">
                        <prettyPrint>false</prettyPrint>
                    </jsonFormatter>
                    <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>
                    <appendLineSeparator>true</appendLineSeparator>
                </layout>
            </encoder>
        </appender>
        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
    <springProfile name="local">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>[%level] %date{ISO8601} %msg\n</pattern>
            </encoder>
        </appender>
        <root level="info">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
</configuration>
