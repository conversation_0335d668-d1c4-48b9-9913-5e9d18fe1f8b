package com.loyalty.nova.offer.state.writer.producer.util

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.loyalty.nova.common.util.generators.ObjectGenerator
import com.loyalty.nova.common.util.nextObjects
import com.loyalty.nova.common.util.nextUUID
import com.loyalty.nova.offer.state.writer.producer.controller.v1.dto.OfferStateChangeDTO
import com.loyalty.nova.offer.state.writer.producer.controller.v1.dto.OfferStateChangeRequestDTO
import com.loyalty.nova.offer.state.writer.producer.controller.v1.dto.OfferStateDTO
import java.util.UUID
import kotlin.random.Random

data class OfferStateChangeRequest(
        val collectorId: String,
        val offerStateChangeRequestDTO: OfferStateChangeRequestDTO
)

class OfferStateChangeRequestObjectGenerator(
        offerIds: List<UUID>,
        collectorIds: List<String>,
        private val states: Map<String, List<String>>
) : ObjectGenerator<OfferStateChangeRequest> {

    constructor(
            random: Random,
            offerCount: Int,
            collectorCount: Int,
            states: Map<String, List<String>>
    ) : this(
            offerIds = (1..offerCount).map { random.nextUUID() },
            collectorIds = (1..collectorCount).map { number -> number.toCollectorId() },
            states = states
    )

    private val offerIds = offerIds

    private val collectorIdStateMapping: Map<String, MutableMap<String, String>> = collectorIds
            .associateWith {
                return@associateWith this.states.keys
                        .associateWith { name -> this.states.getValue(name).first() }
                        .toMutableMap()
            }

    override fun nextObject(random: Random): OfferStateChangeRequest {
        val stateName = this.states.keys.random(random)
        val collectorId = this.collectorIdStateMapping.keys.random(random)
        return OfferStateChangeRequest(
                collectorId = collectorId,
                offerStateChangeRequestDTO = OfferStateChangeRequestDTO(
                        stateChanges = listOf(
                                OfferStateChangeDTO(
                                        offerId = this.offerIds.random(random),
                                        states = listOf(
                                                OfferStateDTO(
                                                        name = stateName,
                                                        value = this.toggleStateValue(random, collectorId, stateName)
                                                )
                                        )
                                )
                        )
                )
        )
    }

    private fun toggleStateValue(random: Random, collectorId: String, stateName: String): String {
        var newStateValue: String
        val oldStateValue = this.collectorIdStateMapping.getValue(collectorId).getValue(stateName)
        do {
            newStateValue = this.states.getValue(stateName).random(random)
        } while (newStateValue == oldStateValue)
        this.collectorIdStateMapping.getValue(collectorId).put(stateName, newStateValue)
        return newStateValue
    }

    companion object {
        private fun Int.toCollectorId(): String {
            return String.format("8%010d", this)
        }
    }
}

// Change these values as desired
internal const val OFFER_COUNT = 10
internal const val COLLECTOR_COUNT = 20
internal const val GENERATED_COUNT = 1000

fun main(vararg args: String) {
    val random = Random(0)

    val objectMapper = jacksonObjectMapper()

    val objectGenerator = OfferStateChangeRequestObjectGenerator(
            random = random,
            offerCount = OFFER_COUNT,
            collectorCount = COLLECTOR_COUNT,
            states = mapOf(
                    "SAVE" to listOf(
                            "UNSAVED",
                            "SAVED"
                    ),
                    "LOAD" to listOf(
                            "UNLOADED",
                            "LOADED"
                    )
            )
    )

    objectGenerator.nextObjects(GENERATED_COUNT, random)
            .map(objectMapper::writeValueAsString)
            .forEach(::println)
}
