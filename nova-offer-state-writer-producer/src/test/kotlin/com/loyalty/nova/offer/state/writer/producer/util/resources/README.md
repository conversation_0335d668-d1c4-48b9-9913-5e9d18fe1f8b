# nova-offer-state-writer, creating test data 

*NOTE* 
while Util.kt is perfect for generating complete event json but couldnt make the postman R<PERSON> read its values, kept it as is but used Util1.kt

1. use Template_Bulk_Upload.csv file attached to bulk offers for different partners and publish
2. export offerIds and PartnerIds with something like this
    SELECT id, partner_id from offers where created_by='<EMAIL>' and end_date='2020-07-24T23:59:00.000Z' and status='PUBLISHED' order by partner_id asc;
3. pass the offerIds to Util1.kt and run its main (sample collectorIds already there) 
4. save the result as an array in json file (state_writer.json in this case but it has more than 5000 records, split it up because of postman run)
5. import the postman collection with its env to postman
6. from collection run with file while selecting the result of generator and let it run with 5 ms delay
    - check the status and make sure you have data received in reader db
this generates the seed data for calling endpoints in reader
7. to add 500000 dummy records for fake collectors, connect to db and run dataGenerator.sql (test with 1 first for possible schema changes)
*NOTE*
BR ADVISED THESE DUMMY RECORDS ARE ONLY TO GIVE VOLUME TO OUR DB AND NOT GOOD FOR REAL TEST IN OUR ENDPOINTS
