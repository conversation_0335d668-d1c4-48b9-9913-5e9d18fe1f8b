{"info": {"_postman_id": "074909f8-ea28-4bea-b21a-bfc3ed823c4e", "name": "StateWriteEventRun", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Producer - Produce group of State Change Events", "event": [{"listen": "test", "script": {"id": "e7693606-20c8-4179-91a3-d293c75b5a9a", "exec": ["// pm.test(\"response has data value\", function(){", "//     var jsonData = pm.response.json();", "//     pm.expect(jsonData.data.foo).to.equal(pm.iterationData.get(\"value\"));", "// })"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "62df02c8-099e-400d-a08d-943d2d5a66c7", "exec": ["// console.log(data.offerStateChangeRequestDTO);", "console.log(pm.variables.get(\"offerStateChangeRequestDTO\"));"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "x-correlation-id", "value": "bcf529ae-0de0-4ded-90f5-e050ce2df0e3", "type": "text"}, {"key": "x-origin-client", "value": "mobile", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"stateChanges\": [\n        {\n            \"offerId\": \"{{offerId}}\",\n            \"states\": [\n                {\n                    \"name\": \"{{stateName}}\",\n                    \"value\": \"{{stateValue}}\",\n                    \"properties\": {\n                    \t\"p_name1\" : \"p_value1\",\n                    \t\"p_name2\": \"p_value2\"\n                    }\n                }\n            ]\n        }\n    ]\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{producerHost}}/state-writer/v1/{{collectorId}}/put", "host": ["{{producerHost}}"], "path": ["state-writer", "v1", "{{collectorId}}", "put"]}, "description": "based on input file adds buch of events to writer"}, "response": []}], "protocolProfileBehavior": {}}