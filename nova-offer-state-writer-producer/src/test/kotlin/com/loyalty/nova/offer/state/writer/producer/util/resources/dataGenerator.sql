DO $$
DECLARE
	offerId UUID;
	collectorID varchar(11);
	collector_count INTEGER := 0 ;
	collector_records INTEGER := 2000;
	offer_records INTEGER := 50 ;
	offer_count INTEGER := 0;
	status_value VARCHAR(10);
	BEGIN
		LOOP
		EXIT WHEN offer_count = offer_records;
			offerID = (SELECT offer_id FROM offer WHERE end_date > (select now()) ORDER BY random() LIMIT 1);
			offer_count := offer_count + 1;
			LOOP
				EXIT WHEN collector_count = collector_records ;
				collector_count := collector_count + 1 ;
					IF random() > 0.5 THEN
						status_value = 'UNSAVED';
					ELSE
						status_value = 'SAVED';
					END IF;
					collectorID = (select floor(random() * 100000000000 + 1)::varchar);

					INSERT INTO collector_offer (offer_id, collector_id)
					VALUES (offerID, collectorID) ON CONFLICT DO NOTHING;

					INSERT INTO collector_offer_state(name, value, collector_id, offer_id, updated_at, properties)
					VALUES ('save', status_value, collectorID, offerID, (SELECT NOW()), '{"pName":"pValue"}') ON CONFLICT DO NOTHING;

					-- PERFORM pg_sleep(1);
			END LOOP ;
			collector_count := 0;
		END LOOP ;
END $$;