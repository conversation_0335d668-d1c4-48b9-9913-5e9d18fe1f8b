pluginManagement {
    apply from: 'dependency.gradle'
    plugins {
        id 'com.jfrog.artifactory' version "${versions.jfrogVersion}"
        id 'org.jetbrains.kotlin.jvm' version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.allope' version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.noarg' version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.spring' version "${versions.kotlinVersion}"
        id 'org.jetbrains.kotlin.plugin.jpa' version "${versions.kotlinVersion}"
        id 'io.spring.dependency-management' version "${versions.springDependencyManagementVersion}"
        id 'org.owasp.dependencycheck' version "${versions.dependencyCheckGradleVersion}"
        id 'org.springframework.boot' version "${versions.springBootVersion}"
        id 'com.gradle.enterprise' version "${versions.gradleEnterpriseVersion}"
        id 'net.researchgate.release' version "${versions.gradleReleaseVersion}"
    }
}

plugins {
    id 'com.gradle.enterprise'
}

rootProject.name = 'nova-offer-state-writer'
include 'nova-offer-state-writer-producer',
        'nova-offer-state-test'
